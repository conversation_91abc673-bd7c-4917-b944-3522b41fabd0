import Mock from 'mockjs'

const BASE_URL = '/cx-monitordata-12345/servlet/workMonitor'

// 1. 综合总话务量统计
Mock.mock(RegExp(BASE_URL), 'post', (options) => {
  const data = JSON.parse(options.body)

  if (data.messageId === 'allCallStat') {
    return {
      result: '000',
      data: {
        ACCEPTANCE_COUNT: Mock.Random.integer(1000, 9999).toString(),
        ACCEPTANCE_CALL_COUNT: Mock.Random.integer(20000, 99999).toString(),
        ACCEPTANCE_FLOW_UP: Mock.Random.integer(10000, 99999).toString(),
        ACCEPTANCE_COUNT_PERCENT: Mock.Random.integer(5, 20) + '%',
        ACCEPTANCE_CALL_COUNT_PERCENT: Mock.Random.integer(30, 50) + '%',
        ACCEPTANCE_FLOW_UP_PERCENT: Mock.Random.integer(20, 40) + '%'
      },
      serialId: data.serialId,
      desc: 'Succ'
    }
  }

  // 2. 上月总和排名
  if (data.messageId === 'lastMonthRanking') {
    const names = data.type === 'agent' ? ['张三', '李四', '王五', '赵六', '孙七', '周八', '吴九', '郑十',
      '钱十一', '陈十二', '徐十三', '杨十四', '魏十五', '黄十六',
      '刘十七', '唐十八', '马十九', '胡二十'] : ['接线一组', '接线二组', '疑难（外呼+内部求助）', '投诉处理组', '督办组']
    const rankList = []

    for (let i = 0; i < 20; i++) {
      rankList.push({
        NAME: names[i],
        ALL_SCORE: (300 - i * 10).toString(),
        MONTHLY_RANKING: (i + 1).toString(),
        AGENT_NO: (10000 + i).toString(),
        MONTH_ID: '2025-05',
        // 五维图
        REWARD: Mock.Random.integer(1, 10).toString(),
        DEDUCTION: Mock.Random.integer(0, 5).toString(),
        ON_HOOK_SATISFACTION: Mock.Random.integer(80, 99).toString(),
        LAST_MONTH_SCORE_FIVE: {
          qualityScoreLeven: Mock.Random.integer(1, 5),
          monthlyExamScoreLeven: Mock.Random.integer(1, 5),
          busiNumberScoreLeven: Mock.Random.integer(1, 5),
          afterLongScoreLeven: Mock.Random.integer(1, 5),
          attendanceScoreLeven: Mock.Random.integer(1, 5),
          monthlyExamScore: Mock.Random.integer(70, 100),
          attendanceScore: Mock.Random.integer(70, 100),
          busiNumberScore: Mock.Random.integer(70, 100),
          qualityScore: Mock.Random.integer(70, 100),
          afterLongScore: Mock.Random.integer(70, 100)
        },
      })
    }

    if (data.keyWord) {
      return {
        result: '000',
        data: rankList.filter(item => item.NAME && item.NAME.includes(data.keyWord)),
        serialId: data.serialId,
        desc: 'Succ'
      }
    }

    return {
      result: '000',
      data: rankList,
      serialId: data.serialId,
      desc: 'Succ'
    }
  }

  // 3. 五维图数据
  if (data.messageId === 'agentFiveWayStat') {
    return {
      result: '000',
      data: {
        reward: Mock.Random.integer(1, 10).toString(),
        monthlyRanking: Mock.Random.integer(1, 20).toString(),
        deduction: Mock.Random.integer(0, 5).toString(),
        lastMonthScoreFive: {
          qualityScoreLeven: Mock.Random.integer(1, 5),
          monthlyExamScoreLeven: Mock.Random.integer(1, 5),
          busiNumberScoreLeven: Mock.Random.integer(1, 5),
          afterLongScoreLeven: Mock.Random.integer(1, 5),
          attendanceScoreLeven: Mock.Random.integer(1, 5),
          monthlyExamScore: Mock.Random.integer(70, 100),
          attendanceScore: Mock.Random.integer(70, 100),
          busiNumberScore: Mock.Random.integer(70, 100),
          qualityScore: Mock.Random.integer(70, 100),
          afterLongScore: Mock.Random.integer(70, 100)
        },
        allScore: Mock.Random.integer(200, 300).toString(),
        onHookSatisfaction: Mock.Random.integer(80, 99).toString()
      },
      serialId: data.serialId,
      desc: 'Succ'
    }
  }

  // 4. 历史通话
  if (data.messageId === 'historyCall') {
    const callList = []

    for (let i = 0; i < 10; i++) {
      callList.push({
        CALL_ID: Mock.Random.guid(),
        ORDER_ID: Mock.Random.guid(),
        AGENT_NAME: Mock.Random.cname(),
        AGENT_PHONE: '0' + Mock.Random.integer(1000000000, 9999999999),
        AGENT_NO: (10000 + i).toString(),
        CUST_PHONE: Mock.Random.integer(13000000000, 19999999999).toString(),
        START_TIME: Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
        END_TIME: Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
        CALL_FLAG: Mock.Random.pick(['呼入', '呼出']),
        CALL_CONTENT: JSON.stringify(Array(10).fill().map(() => ({
          clientId: Mock.Random.pick(['1', '2']),
          txt: Mock.Random.csentence(5, 100),
          timestamp: Mock.Random.datetime('yyyy-MM-dd HH:mm:ss')
        }))),
        CALL_TIME: Mock.Random.integer(60, 600).toString(),
        RECORD_PATH: 'http://localhost:8080/media/test8k.1b3123a9.wav'
      })
    }

    return {
      result: '000',
      data: callList,
      serialId: data.serialId,
      desc: 'Succ'
    }
  }

  // 5. 班组数据
  if (data.messageId === 'queryWorkGroup') {
    const groups = []
    const groupNames = ['接线一组', '接线二组', '接线三组', '接线四组', '接线五组', '接线六组', '接线七组', '接线八组', '接线九组', '接线十组', '疑难处理一组', '疑难处理二组', '投诉处理一组', '投诉处理二组', '督办一组', '督办二组', '外呼一组', '外呼二组', '质检组', '培训组']

    for (let i = 0; i < 20; i++) {
      const userList = []
      const userCount = Mock.Random.integer(500, 800)

      for (let j = 0; j < userCount; j++) {
        userList.push({
          IMG_URL: j % 5 === 0 ? Mock.Random.image('64x64') : '',
          WORKGROUPID: (90 + i).toString(),
          WORK_COUNT: Mock.Random.integer(10, 100).toString(),
          AGENTID: (10000 + i * 1000 + j).toString(),
          NAME: Mock.Random.cname()
        })
      }

      groups.push({
        workGroupWorkCount: Mock.Random.integer(5000, 15000),
        workGroupName: groupNames[i],
        businessType: Mock.Random.pick(['接线', '外呼']),
        workGroupId: (90 + i).toString(),
        userList
      })
    }

    return {
      result: '000',
      data: groups,
      serialId: data.serialId,
      desc: 'Succ'
    }
  }

  // 7. 坐席话务统计信息
  if (data.messageId === 'agentCallStat') {
    return {
      result: '000',
      data: {
        AGENTID: data.agentId,
        CALL_IN_COUNT_ALL: Mock.Random.integer(20, 200).toString(),
        AVG_CALL_IN_TIME: Mock.Random.integer(60, 300).toString(),
        AVG_ARRANGE_TIME: Mock.Random.integer(20, 120).toString(),
        LOGIN_TIME: Mock.Random.integer(14400, 28800).toString(),
        CALL_IN_TIME_ALL: Mock.Random.integer(3600, 14400).toString(),
        GOOD_PERCENT: Mock.Random.integer(5, 20),
      },
      serialId: data.serialId,
      desc: 'Succ'
    }
  }

  // 8. 通话中数据
  if (data.messageId === 'callData') {
    // 随机模拟是否有通话中的数据，有50%概率返回空数据
    const hasCallData = Mock.Random.boolean(0.5, false, true);

    if (!hasCallData) {
      return {
        result: '000',
        data: null,
        serialId: data.serialId,
        desc: 'Succ'
      }
    }

    return {
      result: '000',
      data: {
        CALL_ID: Mock.Random.guid(),
        ORDER_ID: Mock.Random.guid(),
        AGENT_NAME: Mock.Random.cname(),
        AGENT_PHONE: '0' + Mock.Random.integer(1000000000, 9999999999),
        AGENT_NO: data.agentId,
        CUST_PHONE: Mock.Random.integer(13000000000, 19999999999).toString(),
        START_TIME: Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
        END_TIME: '',
        CALL_FLAG: Mock.Random.pick(['呼入', '呼出']),
        CALL_CONTENT: JSON.stringify(Array(5).fill().map(() => ({
          clientId: Mock.Random.pick(['1', '2']),
          txt: Mock.Random.csentence(5, 100),
          timestamp: Mock.Random.datetime('yyyy-MM-dd HH:mm:ss')
        }))),
        CALL_TIME: Mock.Random.integer(30, 180).toString(),
        RECORD_PATH: 'http://localhost:8080/media/test8k.1b3123a9.wav'
      },
      serialId: data.serialId,
      desc: 'Succ'
    }
  }

  // 9. 工单登记
  if (data.messageId === 'workOrderDetail') {
    const sexOptions = ['男', '女']
    const orderTypeOptions = ['投诉', '咨询', '建议', '求助']
    const areaOptions = ['东城区', '西城区', '朝阳区', '海淀区', '丰台区', '石景山区', '门头沟区', '房山区', '通州区', '顺义区']
    const streetOptions = ['街道一', '街道二', '街道三', '街道四', '街道五']
    const officeOptions = ['市政府办公厅', '市发展改革委', '市教委', '市科委', '市公安局', '市民政局', '市司法局', '市财政局']

    return {
      result: '000',
      data: {
        CUST_NAME: Mock.Random.cname(),                   // 姓名
        CUST_SEX: Mock.Random.pick(sexOptions),           // 性别
        CUST_PHONE: Mock.Random.integer(13000000000, 19999999999).toString(),  // 来电号码
        SPECIAL_FLAG: Mock.Random.pick(['0', '1']),       // 来电人要求信息保密 0：否 1：是
        ORDER_TYPE: Mock.Random.pick(orderTypeOptions),   // 工单类型
        REFLECT_NAME: Mock.Random.cname(),                // 被反映者
        TITLE: Mock.Random.ctitle(5, 20),                 // 标题
        MAJOR_CONTENT: Mock.Random.cparagraph(2, 5),      // 主要内容
        AREA_NAME: Mock.Random.pick(areaOptions),         // 所在区县
        STREET_NAME: Mock.Random.pick(streetOptions),     // 街道
        ADDRESS: Mock.Random.county(true) + Mock.Random.csentence(5, 15), // 详细地址
        OFFICE_NAME: Mock.Random.pick(officeOptions)      // 承办单位
      },
      serialId: data.serialId,
      desc: 'Succ'
    }
  }

  return {
    result: '001',
    data: null,
    serialId: data.serialId,
    desc: 'Unknown messageId'
  }
})

export default Mock 